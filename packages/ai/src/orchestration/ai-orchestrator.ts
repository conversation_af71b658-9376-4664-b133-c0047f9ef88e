import { 
  AIRequest, 
  AIResponse, 
  AIProvider, 
  AIOrchestrationStrategy, 
  AIOrchestrationResult,
  AIContext,
  EventBus,
  PlatformEvent
} from '@unified-assistant/types';
import { ProviderManager } from './provider-manager';
import { ContextManager } from './context-manager';
import { v4 as uuidv4 } from 'uuid';

export class AIOrchestrator {
  private providerManager: ProviderManager;
  private contextManager: ContextManager;
  private eventBus?: EventBus;
  private defaultStrategy: AIOrchestrationStrategy;

  constructor(
    providerManager: ProviderManager,
    contextManager: ContextManager,
    eventBus?: EventBus
  ) {
    this.providerManager = providerManager;
    this.contextManager = contextManager;
    this.eventBus = eventBus;
    this.defaultStrategy = {
      type: 'fallback',
      providers: ['openai', 'anthropic'],
      config: { maxRetries: 2, timeout: 30000 }
    };
  }

  /**
   * Process an AI request using the specified or default orchestration strategy
   */
  async processRequest(
    request: AIRequest,
    strategy?: AIOrchestrationStrategy
  ): Promise<AIOrchestrationResult> {
    const orchestrationStrategy = strategy || this.defaultStrategy;
    const startTime = Date.now();

    // Emit request start event
    this.emitEvent('ai:request:start', {
      requestId: request.id,
      type: request.type,
      strategy: orchestrationStrategy
    });

    try {
      let result: AIOrchestrationResult;

      switch (orchestrationStrategy.type) {
        case 'single':
          result = await this.processSingleProvider(request, orchestrationStrategy);
          break;
        case 'fallback':
          result = await this.processFallbackStrategy(request, orchestrationStrategy);
          break;
        case 'parallel':
          result = await this.processParallelStrategy(request, orchestrationStrategy);
          break;
        case 'consensus':
          result = await this.processConsensusStrategy(request, orchestrationStrategy);
          break;
        case 'routing':
          result = await this.processRoutingStrategy(request, orchestrationStrategy);
          break;
        default:
          throw new Error(`Unknown orchestration strategy: ${orchestrationStrategy.type}`);
      }

      result.totalLatency = Date.now() - startTime;

      // Emit success event
      this.emitEvent('ai:request:success', {
        requestId: request.id,
        result,
        latency: result.totalLatency
      });

      return result;
    } catch (error) {
      // Emit error event
      this.emitEvent('ai:request:error', {
        requestId: request.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        latency: Date.now() - startTime
      });

      throw error;
    }
  }

  /**
   * Process request with a single provider
   */
  private async processSingleProvider(
    request: AIRequest,
    strategy: AIOrchestrationStrategy
  ): Promise<AIOrchestrationResult> {
    const providerId = strategy.providers[0];
    const provider = await this.providerManager.getProvider(providerId);
    
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    const response = await provider.processRequest(request);
    
    return {
      primary: response,
      strategy,
      totalLatency: response.latency,
      totalCost: response.usage.cost || 0
    };
  }

  /**
   * Process request with fallback strategy
   */
  private async processFallbackStrategy(
    request: AIRequest,
    strategy: AIOrchestrationStrategy
  ): Promise<AIOrchestrationResult> {
    const errors: Error[] = [];
    let totalCost = 0;

    for (const providerId of strategy.providers) {
      try {
        const provider = await this.providerManager.getProvider(providerId);
        if (!provider) {
          errors.push(new Error(`Provider ${providerId} not found`));
          continue;
        }

        const response = await provider.processRequest(request);
        totalCost += response.usage.cost || 0;

        return {
          primary: response,
          strategy,
          totalLatency: response.latency,
          totalCost
        };
      } catch (error) {
        errors.push(error instanceof Error ? error : new Error('Unknown error'));
        continue;
      }
    }

    throw new Error(`All providers failed: ${errors.map(e => e.message).join(', ')}`);
  }

  /**
   * Process request with parallel strategy (fastest response wins)
   */
  private async processParallelStrategy(
    request: AIRequest,
    strategy: AIOrchestrationStrategy
  ): Promise<AIOrchestrationResult> {
    const promises = strategy.providers.map(async (providerId) => {
      const provider = await this.providerManager.getProvider(providerId);
      if (!provider) {
        throw new Error(`Provider ${providerId} not found`);
      }
      return provider.processRequest(request);
    });

    const responses = await Promise.allSettled(promises);
    const successful = responses
      .filter((result): result is PromiseFulfilledResult<AIResponse> => 
        result.status === 'fulfilled'
      )
      .map(result => result.value);

    if (successful.length === 0) {
      throw new Error('All parallel providers failed');
    }

    // Return fastest response as primary
    const primary = successful.reduce((fastest, current) => 
      current.latency < fastest.latency ? current : fastest
    );

    const alternatives = successful.filter(response => response.id !== primary.id);
    const totalCost = successful.reduce((sum, response) => sum + (response.usage.cost || 0), 0);

    return {
      primary,
      alternatives,
      strategy,
      totalLatency: primary.latency,
      totalCost
    };
  }

  /**
   * Process request with consensus strategy (majority vote)
   */
  private async processConsensusStrategy(
    request: AIRequest,
    strategy: AIOrchestrationStrategy
  ): Promise<AIOrchestrationResult> {
    // Implementation would involve running multiple providers and choosing based on consensus
    // For now, fallback to parallel strategy
    return this.processParallelStrategy(request, strategy);
  }

  /**
   * Process request with intelligent routing
   */
  private async processRoutingStrategy(
    request: AIRequest,
    strategy: AIOrchestrationStrategy
  ): Promise<AIOrchestrationResult> {
    // Route based on request type, context, or other criteria
    const routedProviderId = this.routeRequest(request, strategy);
    
    return this.processSingleProvider(request, {
      ...strategy,
      type: 'single',
      providers: [routedProviderId]
    });
  }

  /**
   * Route request to appropriate provider based on criteria
   */
  private routeRequest(request: AIRequest, strategy: AIOrchestrationStrategy): string {
    // Simple routing logic - can be enhanced with ML-based routing
    if (request.type === 'image') {
      return strategy.providers.find(p => p.includes('dalle') || p.includes('midjourney')) || strategy.providers[0];
    }
    
    if (request.type === 'code') {
      return strategy.providers.find(p => p.includes('openai') || p.includes('anthropic')) || strategy.providers[0];
    }

    return strategy.providers[0];
  }

  /**
   * Get orchestrator metrics
   */
  async getMetrics() {
    const providerMetrics = await this.providerManager.getAllMetrics();
    
    return {
      providers: providerMetrics,
      totalRequests: Object.values(providerMetrics).reduce((sum, metrics) => sum + metrics.requests, 0),
      totalErrors: Object.values(providerMetrics).reduce((sum, metrics) => sum + metrics.errors, 0),
      averageLatency: Object.values(providerMetrics).reduce((sum, metrics) => sum + metrics.averageLatency, 0) / Object.keys(providerMetrics).length,
      totalCost: Object.values(providerMetrics).reduce((sum, metrics) => sum + metrics.cost, 0)
    };
  }

  /**
   * Update default orchestration strategy
   */
  setDefaultStrategy(strategy: AIOrchestrationStrategy) {
    this.defaultStrategy = strategy;
  }

  /**
   * Emit event through event bus if available
   */
  private emitEvent(type: string, payload: any) {
    if (this.eventBus) {
      const event: PlatformEvent = {
        id: uuidv4(),
        type,
        source: 'ai-orchestrator',
        payload,
        timestamp: new Date(),
        priority: 'normal',
        persistent: false
      };
      
      this.eventBus.emit(event);
    }
  }
}
