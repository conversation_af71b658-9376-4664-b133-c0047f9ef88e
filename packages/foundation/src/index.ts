// Platform core
export * from './platform/unified-platform';
export * from './platform/module-registry';
export * from './platform/platform-config';

// Module integration
export * from './modules/base-module';
export * from './modules/module-manager';
export * from './modules/communication-bridge';

// State management
export * from './state/platform-store';
export * from './state/module-state-manager';
export * from './state/shared-context-store';

// Integration utilities
export * from './integration/agent-inbox-integration';
export * from './integration/module-loader';
export * from './integration/cross-module-router';

// React hooks and providers
export * from './hooks/use-platform';
export * from './hooks/use-module-communication';
export * from './hooks/use-shared-context';
export * from './providers/platform-provider';
export * from './providers/module-provider';
