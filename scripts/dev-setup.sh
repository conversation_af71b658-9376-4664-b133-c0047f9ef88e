#!/bin/bash

# Unified Development Setup Script
# 
# LEVER approach implementation:
# L - Leverage existing development patterns from individual modules
# E - Extend with unified development environment
# V - Verify through comprehensive setup validation
# E - Eliminate duplicate setup across modules
# R - Reduce complexity through automated setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NODE_VERSION="18"
PNPM_VERSION="8.15.0"
REQUIRED_TOOLS=("node" "pnpm" "git")

echo -e "${BLUE}🚀 Unified AI Assistant Platform - Development Setup${NC}"
echo -e "${BLUE}=================================================${NC}"

# Function to print status messages
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Node.js version
check_node_version() {
    if command_exists node; then
        local node_version=$(node --version | sed 's/v//')
        local major_version=$(echo $node_version | cut -d. -f1)
        
        if [ "$major_version" -ge "$NODE_VERSION" ]; then
            print_status "Node.js version $node_version is compatible"
            return 0
        else
            print_error "Node.js version $node_version is too old. Required: $NODE_VERSION+"
            return 1
        fi
    else
        print_error "Node.js is not installed"
        return 1
    fi
}

# Function to check pnpm version
check_pnpm_version() {
    if command_exists pnpm; then
        local pnpm_version=$(pnpm --version)
        print_status "pnpm version $pnpm_version is installed"
        return 0
    else
        print_warning "pnpm is not installed"
        return 1
    fi
}

# Function to install pnpm
install_pnpm() {
    print_info "Installing pnpm..."
    if command_exists npm; then
        npm install -g pnpm@$PNPM_VERSION
        print_status "pnpm installed successfully"
    else
        print_error "npm is required to install pnpm"
        exit 1
    fi
}

# Function to check required tools
check_requirements() {
    print_info "Checking system requirements..."
    
    local missing_tools=()
    
    for tool in "${REQUIRED_TOOLS[@]}"; do
        if ! command_exists "$tool"; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_info "Please install the missing tools and run this script again"
        exit 1
    fi
    
    # Check Node.js version
    if ! check_node_version; then
        print_info "Please install Node.js $NODE_VERSION+ and run this script again"
        exit 1
    fi
    
    # Check/install pnpm
    if ! check_pnpm_version; then
        install_pnpm
    fi
    
    print_status "All system requirements are met"
}

# Function to setup environment variables
setup_environment() {
    print_info "Setting up environment variables..."
    
    # Create .env.local if it doesn't exist
    if [ ! -f ".env.local" ]; then
        cp .env.example .env.local 2>/dev/null || {
            cat > .env.local << EOF
# Unified AI Assistant Platform - Local Environment
NODE_ENV=development

# Database
DATABASE_URL=postgresql://localhost:5432/unified_assistant

# AI Providers (add your API keys)
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GOOGLE_AI_API_KEY=

# Authentication
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# Supabase (if using)
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Feature Flags
NEXT_PUBLIC_ENABLE_AI_ORCHESTRATION=true
NEXT_PUBLIC_ENABLE_CROSS_MODULE_COMMUNICATION=true
NEXT_PUBLIC_ENABLE_SHARED_STATE=true
EOF
        }
        print_status "Environment file created: .env.local"
        print_warning "Please update .env.local with your API keys and configuration"
    else
        print_status "Environment file already exists: .env.local"
    fi
}

# Function to install dependencies
install_dependencies() {
    print_info "Installing dependencies..."
    
    # Install root dependencies
    pnpm install
    
    print_status "Dependencies installed successfully"
}

# Function to build shared packages
build_packages() {
    print_info "Building shared packages..."
    
    # Build packages in dependency order
    pnpm run build:packages
    
    print_status "Shared packages built successfully"
}

# Function to setup git hooks
setup_git_hooks() {
    print_info "Setting up git hooks..."
    
    # Create pre-commit hook
    mkdir -p .git/hooks
    
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Pre-commit hook for unified platform

echo "Running pre-commit checks..."

# Run linting
echo "Checking code style..."
pnpm run lint

# Run type checking
echo "Checking TypeScript..."
pnpm run type-check

# Run tests
echo "Running tests..."
pnpm run test

echo "Pre-commit checks passed!"
EOF
    
    chmod +x .git/hooks/pre-commit
    
    print_status "Git hooks configured"
}

# Function to verify setup
verify_setup() {
    print_info "Verifying setup..."
    
    # Check if packages build successfully
    if pnpm run build:packages > /dev/null 2>&1; then
        print_status "Package builds are working"
    else
        print_error "Package builds are failing"
        return 1
    fi
    
    # Check if linting works
    if pnpm run lint > /dev/null 2>&1; then
        print_status "Linting is working"
    else
        print_warning "Linting has issues (this is normal for new setup)"
    fi
    
    # Check if type checking works
    if pnpm run type-check > /dev/null 2>&1; then
        print_status "TypeScript checking is working"
    else
        print_warning "TypeScript checking has issues (this is normal for new setup)"
    fi
    
    print_status "Setup verification completed"
}

# Function to display next steps
show_next_steps() {
    echo ""
    echo -e "${GREEN}🎉 Development environment setup completed!${NC}"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Update .env.local with your API keys and configuration"
    echo "2. Start the development environment:"
    echo -e "   ${YELLOW}pnpm run dev${NC}"
    echo ""
    echo -e "${BLUE}Available commands:${NC}"
    echo -e "   ${YELLOW}pnpm run dev${NC}                 - Start all modules in development mode"
    echo -e "   ${YELLOW}pnpm run dev:agent-inbox${NC}     - Start agent-inbox module"
    echo -e "   ${YELLOW}pnpm run dev:foto-fun${NC}        - Start foto-fun module"
    echo -e "   ${YELLOW}pnpm run dev:vibe-kanban${NC}     - Start vibe-kanban module"
    echo -e "   ${YELLOW}pnpm run build${NC}               - Build all packages and modules"
    echo -e "   ${YELLOW}pnpm run test${NC}                - Run all tests"
    echo -e "   ${YELLOW}pnpm run lint${NC}                - Lint all code"
    echo -e "   ${YELLOW}pnpm run type-check${NC}          - Check TypeScript types"
    echo ""
    echo -e "${BLUE}Documentation:${NC}"
    echo "- Architecture Guide: ./docs/architecture.md"
    echo "- Development Guide: ./docs/development.md"
    echo "- Module Integration: ./docs/module-integration.md"
    echo ""
}

# Main execution
main() {
    check_requirements
    setup_environment
    install_dependencies
    build_packages
    setup_git_hooks
    verify_setup
    show_next_steps
}

# Run main function
main "$@"
