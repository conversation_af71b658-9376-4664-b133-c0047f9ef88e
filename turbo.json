{"$schema": "https://turbo.build/schema.json", "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "build/**", "out/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_*", "SUPABASE_*", "OPENAI_*", "ANTHROPIC_*"]}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV", "NEXT_PUBLIC_*", "SUPABASE_*", "OPENAI_*", "ANTHROPIC_*"]}, "lint": {"dependsOn": ["^build"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"], "env": ["NODE_ENV"]}, "test:integration": {"dependsOn": ["^build"], "outputs": ["coverage/**"], "env": ["NODE_ENV", "TEST_*"]}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "clean": {"cache": false, "outputs": []}, "storybook": {"dependsOn": ["^build"], "cache": false, "persistent": true}, "build-storybook": {"dependsOn": ["^build"], "outputs": ["storybook-static/**"]}}, "globalDependencies": ["package.json", "turbo.json", "tsconfig.json", ".env", ".env.local"], "globalEnv": ["NODE_ENV", "CI", "VERCEL", "VERCEL_ENV"]}